import { createSlice, createAsyncThunk } from '@reduxjs/toolkit'
import { db, auth } from '../config/firebase.config'
import { collection, getDocs, addDoc, updateDoc, deleteDoc, doc } from 'firebase/firestore'
import { COLLECTIONS } from '../constants'

// Async thunks
export const fetchCategoriesThunk = createAsyncThunk(
  'categories/fetchCategories',
  async (_, { rejectWithValue }) => {
    try {
      const querySnapshot = await getDocs(collection(db, COLLECTIONS.CATEGORIES))

      // Create maps for both parent categories and subcategories
      const categoriesMap = new Map()
      const subcategoriesMap = new Map()

      querySnapshot.forEach((doc) => {
        const categoryData = { id: doc.id, ...doc.data() }

        // If it has a parentCategoryId, it's a subcategory
        if (categoryData.parentCategoryId) {
          if (!subcategoriesMap.has(categoryData.parentCategoryId)) {
            subcategoriesMap.set(categoryData.parentCategoryId, [])
          }
          subcategoriesMap.get(categoryData.parentCategoryId).push(categoryData)
        } else {
          // If it doesn't have a parentCategoryId, it's a parent category
          categoriesMap.set(doc.id, categoryData)
        }
      })

      // Combine the parent categories with their subcategories
      const categoriesWithSubcategories = Array.from(categoriesMap.values()).map((category) => {
        return {
          ...category,
          subcategories: subcategoriesMap.get(category.id) || [],
        }
      })

      return categoriesWithSubcategories
    } catch (error) {
      return rejectWithValue(error.message)
    }
  },
)
export const addCategoryThunk = createAsyncThunk(
  'categories/addCategory',
  async (categoryData, { rejectWithValue }) => {
    try {
      const docRef = await addDoc(collection(db, COLLECTIONS.CATEGORIES), categoryData)
      return { id: docRef.id, ...categoryData }
    } catch (error) {
      return rejectWithValue(error.message)
    }
  },
)

export const updateCategoryThunk = createAsyncThunk(
  'categories/updateCategory',
  async (categoryData, { rejectWithValue }) => {
    try {
      const { id, ...data } = categoryData
      await updateDoc(doc(db, COLLECTIONS.CATEGORIES, id), data)
      return categoryData
    } catch (error) {
      return rejectWithValue(error.message)
    }
  },
)

export const deleteCategoryThunk = createAsyncThunk(
  'categories/deleteCategory',
  async (id, { rejectWithValue, getState }) => {
    try {
      // First, delete the parent category
      await deleteDoc(doc(db, COLLECTIONS.CATEGORIES, id))

      // Get all categories from state
      const { categories } = getState().categories

      // Find subcategories that belong to this parent
      const subcategories = categories.filter(
        (cat) => cat.parentCategory === id || cat.parentCategoryId === id,
      )

      // Delete all subcategories
      const deletePromises = subcategories.map((subcategory) =>
        deleteDoc(doc(db, COLLECTIONS.CATEGORIES, subcategory.id)),
      )

      // Wait for all subcategory deletions to complete
      await Promise.all(deletePromises)

      // Return both the parent ID and subcategory IDs for state updates
      return {
        parentId: id,
        subcategoryIds: subcategories.map((sub) => sub.id),
      }
    } catch (error) {
      return rejectWithValue(error.message)
    }
  },
)

const categoriesSlice = createSlice({
  name: 'categories',
  initialState: {
    categories: [],
    isLoading: false,
    isAdding: false,
    isUpdating: false,
    isDeleting: false,
    error: null,
  },
  reducers: {
    addNewCategoryAction: (state, { payload }) => {
      state.categories = [payload, ...state.categories]
      return state
    },
  },
  extraReducers: (builder) => {
    builder
      // Fetch categories
      .addCase(fetchCategoriesThunk.pending, (state) => {
        state.isLoading = true
        state.error = null
      })
      .addCase(fetchCategoriesThunk.fulfilled, (state, action) => {
        state.isLoading = false
        state.categories = action.payload
      })
      .addCase(fetchCategoriesThunk.rejected, (state, action) => {
        state.isLoading = false
        state.error = action.payload
      })
      // Add category
      .addCase(addCategoryThunk.pending, (state) => {
        state.isAdding = true
        state.error = null
      })
      .addCase(addCategoryThunk.fulfilled, (state, action) => {
        state.isAdding = false
        state.categories.push(action.payload)
      })
      .addCase(addCategoryThunk.rejected, (state, action) => {
        state.isAdding = false
        state.error = action.payload
      })
      // Update category
      .addCase(updateCategoryThunk.pending, (state) => {
        state.isUpdating = true
        state.error = null
      })
      .addCase(updateCategoryThunk.fulfilled, (state, action) => {
        state.isUpdating = false
        const index = state.categories.findIndex((cat) => cat.id === action.payload.id)
        if (index !== -1) {
          state.categories[index] = action.payload
        }
      })
      .addCase(updateCategoryThunk.rejected, (state, action) => {
        state.isUpdating = false
        state.error = action.payload
      })

      .addCase(deleteCategoryThunk.pending, (state) => {
        state.isDeleting = true
        state.error = null
      })
      .addCase(deleteCategoryThunk.fulfilled, (state, action) => {
        state.isDeleting = false

        state.categories = state.categories.filter((cat) => cat.id !== action.payload.parentId)

        state.categories = state.categories.filter(
          (cat) => !action.payload.subcategoryIds.includes(cat.id),
        )
      })
      .addCase(deleteCategoryThunk.rejected, (state, action) => {
        state.isDeleting = false
        state.error = action.payload
      })
  },
})

// EXPORT ACTIONS
export const { addNewCategoryAction } = categoriesSlice.actions

export default categoriesSlice
