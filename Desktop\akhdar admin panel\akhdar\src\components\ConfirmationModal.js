/* eslint-disable react/prop-types */
import { CButton, CModal, CModalBody, CSpinner } from '@coreui/react'
import React from 'react'
import { FaQuestion } from 'react-icons/fa'

const ConfirmationModal = ({
  title,
  subtitle,
  confirmLabel,
  confirmColor,
  visible,
  closeModal,
  isLoading,
  onConfirm,
}) => {
  return (
    <>
      <CModal visible={visible} alignment="center" onClose={closeModal}>
        <CModalBody
          className="pb-0 d-flex justify-content-center align-items-center flex-column"
          style={{ aspectRatio: '1.6/1' }}
        >
          <div className="d-flex justify-content-center align-items-center flex-column mb-4">
            <FaQuestion style={{ height: 60, width: 60 }} className="mb-4" />
            <h2>{title || 'Confrim?'}</h2>
            <h5>{subtitle}</h5>
          </div>

          <div className="d-flex gap-4 w-100">
            <CButton
              color="secondary"
              variant="outline"
              className="flex-grow-1"
              disabled={isLoading}
              onClick={closeModal}
            >
              Cancel
            </CButton>
            <CButton
              color={confirmColor || 'primary'}
              className="flex-grow-1 text-white"
              disabled={isLoading}
              onClick={onConfirm}
            >
              {isLoading ? <CSpinner size="sm" /> : confirmLabel || 'Confirm'}
            </CButton>
          </div>
        </CModalBody>
      </CModal>
    </>
  )
}

export default ConfirmationModal
