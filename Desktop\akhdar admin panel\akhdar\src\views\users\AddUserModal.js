import React, { useState } from 'react'
import {
  CButton,
  CModal,
  CModalHeader,
  CModalTitle,
  CModalBody,
  CModalFooter,
  CForm,
  CFormInput,
  CFormLabel,
  CFormSelect,
  CSpinner,
} from '@coreui/react'
import { useDispatch, useSelector } from 'react-redux'

const AddUserModal = ({ visible, onClose }) => {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    password: '',
    userType: 'staff',
  })

  const [errors, setErrors] = useState({})
  const dispatch = useDispatch()
  const { isCreating } = useSelector((state) => state.users)

  const handleChange = (e) => {
    const { name, value } = e.target
    setFormData({
      ...formData,
      [name]: value,
    })

    // Clear error when user types
    if (errors[name]) {
      setErrors({
        ...errors,
        [name]: null,
      })
    }
  }

  const validateForm = () => {
    const newErrors = {}

    if (!formData.name.trim()) {
      newErrors.name = 'Name is required'
    }

    if (!formData.email.trim()) {
      newErrors.email = 'Email is required'
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = 'Email is invalid'
    }
    if (!formData.password.trim()) {
      newErrors.password = 'Password is required'
    }

    if (!formData.userType) {
      newErrors.userType = 'User type is required'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = async (e) => {
    e.preventDefault()

    if (validateForm()) {
      // await dispatch(createUserThunk(formData))
      onClose()
      setFormData({
        name: '',
        email: '',
        password: '',
        userType: 'staff',
      })
    }
  }

  return (
    <CModal visible={visible} onClose={onClose} backdrop="static">
      <CModalHeader>
        <CModalTitle>Add New User</CModalTitle>
      </CModalHeader>
      <CModalBody>
        <CForm onSubmit={handleSubmit}>
          <div className="mb-3">
            <CFormLabel htmlFor="name">Name</CFormLabel>
            <CFormInput
              type="text"
              id="name"
              name="name"
              value={formData.name}
              onChange={handleChange}
              invalid={!!errors.name}
            />
            {errors.name && <div className="text-danger">{errors.name}</div>}
          </div>

          <div className="mb-3">
            <CFormLabel htmlFor="email">Email</CFormLabel>
            <CFormInput
              type="email"
              id="email"
              name="email"
              value={formData.email}
              onChange={handleChange}
              invalid={!!errors.email}
            />
            {errors.email && <div className="text-danger">{errors.email}</div>}
          </div>

          <div className="mb-3">
            <CFormLabel htmlFor="password">Password</CFormLabel>
            <CFormInput
              type="password"
              id="password"
              name="password"
              value={formData.password}
              onChange={handleChange}
              invalid={!!errors.password}
            />
            {errors.password && <div className="text-danger">{errors.password}</div>}
          </div>

          <div className="mb-3">
            <CFormLabel htmlFor="userType">User Type</CFormLabel>
            <CFormSelect
              id="userType"
              name="userType"
              value={formData.userType}
              onChange={handleChange}
              invalid={!!errors.userType}
            >
              <option value="manager">Manager</option>
              <option value="staff">Staff</option>
              <option value="client">Client</option>
            </CFormSelect>
            {errors.userType && <div className="text-danger">{errors.userType}</div>}
          </div>
        </CForm>
      </CModalBody>
      <CModalFooter>
        <CButton style={{ backgroundColor: '', color: '#BE935E' }} onClick={onClose}>
          Cancel
        </CButton>
        <CButton
          style={{ backgroundColor: '#BE935E', color: 'white' }}
          onClick={handleSubmit}
          disabled={isCreating}
        >
          {isCreating ? <CSpinner size="sm" className="me-2" /> : null}
          Save User
        </CButton>
      </CModalFooter>
    </CModal>
  )
}

export default AddUserModal
