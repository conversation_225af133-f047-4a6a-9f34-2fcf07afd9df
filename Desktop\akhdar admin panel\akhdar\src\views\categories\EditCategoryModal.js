import React, { useState, useEffect } from 'react'
import {
  CButton,
  CModal,
  CModalHeader,
  CModalTitle,
  CModalBody,
  CModalFooter,
  CForm,
  CFormInput,
  CFormLabel,
  CSpinner,
  CImage,
  CFormText,
  CInputGroup,
} from '@coreui/react'
import { useDispatch, useSelector } from 'react-redux'
import { updateCategoryThunk } from '../../store/categories.slice'
import { getStorage, ref, uploadBytes, getDownloadURL } from 'firebase/storage'
import { collection, addDoc, getDocs, deleteDoc, doc } from 'firebase/firestore'
import { db } from '../../config/firebase.config'
import { COLLECTIONS } from '../../constants/index'

const EditCategoryModal = ({ visible, onClose, category, parentCategories }) => {
  const [formData, setFormData] = useState({
    id: '',
    title: '',
    icon: { url: '' },
    subcategories: [],
  })
  const [uploadedImage, setUploadedImage] = useState(null)
  const [previewUrl, setPreviewUrl] = useState('')
  const [isUploading, setIsUploading] = useState(false)
  const [errors, setErrors] = useState({})
  const [subcategoryInput, setSubcategoryInput] = useState('')
  const [existingSubcategories, setExistingSubcategories] = useState([])
  const dispatch = useDispatch()
  const { isUpdating } = useSelector((state) => state.categories || { isUpdating: false })

  // Load category data when component mounts or category changes
  useEffect(() => {
    if (category) {
      setFormData({
        id: category.id,
        title: category.title || '',
        icon: category.icon || { url: '' },
      })

      if (category.icon?.url) {
        setPreviewUrl(category.icon.url)
      }

      // Load subcategories for this category
      fetchSubcategories(category.id)
    }
  }, [category])

  const fetchSubcategories = async (categoryId) => {
    try {
      const querySnapshot = await getDocs(collection(db, COLLECTIONS.CATEGORIES))
      const subs = []

      querySnapshot.forEach((doc) => {
        const data = doc.data()
        if (data.parentCategory === categoryId) {
          subs.push({ id: doc.id, ...data })
        }
      })

      setExistingSubcategories(subs)

      // Set subcategory titles to formData
      setFormData((prev) => ({
        ...prev,
        subcategories: subs.map((sub) => sub.title),
      }))
    } catch (error) {
      console.error('Error fetching subcategories:', error)
    }
  }

  const handleChange = (e) => {
    const { name, value } = e.target

    if (name === 'iconUrl') {
      setFormData({
        ...formData,
        icon: { url: value },
      })
    } else {
      setFormData({
        ...formData,
        [name]: value,
      })
    }

    // Clear error when user types
    if (errors[name]) {
      setErrors({
        ...errors,
        [name]: null,
      })
    }
  }

  const handleSubcategoryInputChange = (e) => {
    setSubcategoryInput(e.target.value)
  }

  const addSubcategory = () => {
    if (subcategoryInput.trim()) {
      setFormData({
        ...formData,
        subcategories: [...formData.subcategories, subcategoryInput.trim()],
      })
      setSubcategoryInput('') // Clear the input after adding
    }
  }

  const removeSubcategory = (index) => {
    const updatedSubcategories = [...formData.subcategories]
    updatedSubcategories.splice(index, 1)
    setFormData({
      ...formData,
      subcategories: updatedSubcategories,
    })
  }

  const handleFileChange = (e) => {
    const file = e.target.files[0]
    if (!file) return

    // Clear previous errors
    if (errors.icon) {
      setErrors({
        ...errors,
        icon: null,
      })
    }

    // Validate file type
    const validTypes = ['image/jpeg', 'image/png', 'image/webp']
    if (!validTypes.includes(file.type)) {
      setErrors({
        ...errors,
        icon: 'Invalid file type. Please upload a JPEG, PNG, or WEBP image.',
      })
      return
    }

    // Validate file size (max 2MB)
    if (file.size > 2 * 1024 * 1024) {
      setErrors({
        ...errors,
        icon: 'File is too large. Maximum size is 2MB.',
      })
      return
    }

    setUploadedImage(file)

    // Create preview
    const reader = new FileReader()
    reader.onloadend = () => {
      setPreviewUrl(reader.result)
    }
    reader.readAsDataURL(file)
  }

  const uploadIcon = async (file) => {
    if (!file) return null

    try {
      setIsUploading(true)
      const storage = getStorage()
      const timestamp = new Date().getTime()
      const fileName = `category-icons/${timestamp}_${file.name.replace(/\s+/g, '_')}`
      const storageRef = ref(storage, fileName)

      await uploadBytes(storageRef, file)
      const downloadUrl = await getDownloadURL(storageRef)

      return downloadUrl
    } catch (error) {
      console.error('Error uploading file:', error)
      setErrors({
        ...errors,
        icon: 'Failed to upload image. Please try again.',
      })
      return null
    } finally {
      setIsUploading(false)
    }
  }

  const validateForm = () => {
    const newErrors = {}

    if (!formData.title.trim()) {
      newErrors.title = 'Title is required'
    }

    // For editing, we don't need to require an icon if one already exists
    if (!uploadedImage && !formData.icon?.url && !previewUrl) {
      newErrors.icon = 'Please upload an icon or provide an icon URL'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = async (e) => {
    e.preventDefault()

    if (validateForm()) {
      try {
        // Update the main category data
        const categoryData = {
          id: formData.id,
          title: formData.title,
          icon: formData.icon,
          updatedAt: new Date(),
        }

        // If there's an uploaded image, upload it to Firebase Storage
        if (uploadedImage) {
          const iconUrl = await uploadIcon(uploadedImage)
          if (iconUrl) {
            categoryData.icon = { url: iconUrl }
          } else {
            return // Stop if upload failed
          }
        }

        // Update the main category
        await dispatch(updateCategoryThunk(categoryData))

        // Handle subcategories - compare existing with current list
        const currentSubTitles = formData.subcategories
        const existingSubTitles = existingSubcategories.map((sub) => sub.title)

        // Find new subcategories to add
        const newSubcategories = currentSubTitles.filter(
          (title) => !existingSubTitles.includes(title),
        )

        // Create new subcategories
        const subcategoryPromises = newSubcategories.map(async (subcategoryTitle) => {
          const subcategoryData = {
            title: subcategoryTitle,
            parentCategory: formData.id,
            createdAt: new Date(),
          }
          return addDoc(collection(db, COLLECTIONS.CATEGORIES), subcategoryData)
        })

        // Find removed subcategories
        const removedSubs = existingSubcategories.filter(
          (sub) => !currentSubTitles.includes(sub.title),
        )

        // Delete removed subcategories
        const deletePromises = removedSubs.map((sub) =>
          deleteDoc(doc(db, COLLECTIONS.CATEGORIES, sub.id)),
        )

        // Execute all promises
        await Promise.all([...subcategoryPromises, ...deletePromises])

        onClose()
      } catch (error) {
        console.error('Error updating category:', error)
        setErrors({
          ...errors,
          form: 'Failed to update category. Please try again.',
        })
      }
    }
  }

  return (
    <CModal visible={visible} onClose={onClose} backdrop="static">
      <CModalHeader>
        <CModalTitle>Edit Category</CModalTitle>
      </CModalHeader>
      <CModalBody>
        <CForm onSubmit={handleSubmit}>
          <div className="mb-3">
            <CFormLabel htmlFor="title">Title</CFormLabel>
            <CFormInput
              type="text"
              id="title"
              name="title"
              value={formData.title}
              onChange={handleChange}
              invalid={!!errors.title}
            />
            {errors.title && <div className="text-danger">{errors.title}</div>}
          </div>
          <div className="mb-3">
            <CFormLabel>Icon</CFormLabel>
            <div className="mb-2">
              <input
                type="file"
                className="form-control"
                id="iconFile"
                accept=".png,.jpg,.jpeg,.webp"
                onChange={handleFileChange}
              />
              <CFormText>Upload a new image (JPEG, PNG, WEBP, max 2MB)</CFormText>
            </div>

            {previewUrl && (
              <div className="mb-3 text-center">
                <p>Preview:</p>
                <CImage src={previewUrl} width={80} height={80} className="border rounded p-1" />
              </div>
            )}

            {errors.icon && <div className="text-danger mt-2">{errors.icon}</div>}
          </div>
          <div className="mb-3">
            <CFormLabel>Subcategories</CFormLabel>
            <CInputGroup className="mb-2">
              <CFormInput
                placeholder="Enter subcategory"
                value={subcategoryInput}
                onChange={handleSubcategoryInputChange}
                onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addSubcategory())}
                aria-describedby="button-add-subcategory"
              />
              <CButton
                type="button"
                style={{ backgroundColor: '#BE935E', color: '#fff' }}
                id="button-add-subcategory"
                onClick={addSubcategory}
              >
                Add
              </CButton>
            </CInputGroup>

            {formData.subcategories && formData.subcategories.length > 0 && (
              <div className="mt-2">
                <p className="mb-2 font-medium">Subcategories:</p>
                <div className="d-flex flex-wrap gap-2">
                  {/* Display subcategories */}
                  {formData.subcategories.map((subcategory, index) => (
                    <div
                      key={index}
                      className="d-flex justify-content-center align-items-center bg-secondary bg-opacity-10 text-dark px-2 rounded"
                      style={{ fontWeight: '600', fontSize: '14px' }}
                    >
                      <span>{subcategory}</span>
                      <CButton
                        color="light"
                        size="sm"
                        className="ms-2 p-0 border-0"
                        style={{
                          fontSize: '16px',
                          lineHeight: '1',
                          color: 'gray',
                        }}
                        onClick={() => removeSubcategory(index)}
                      >
                        ×
                      </CButton>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
          {errors.form && <div className="text-danger mb-3">{errors.form}</div>}
        </CForm>
      </CModalBody>
      <CModalFooter>
        <CButton
          style={{ backgroundColor: '#BE935E', color: '#fff' }}
          onClick={handleSubmit}
          disabled={isUpdating || isUploading}
        >
          {isUpdating || isUploading ? (
            <>
              <CSpinner size="sm" className="me-2" />
              {isUploading ? 'Uploading...' : 'Updating...'}
            </>
          ) : (
            'Update Category'
          )}
        </CButton>
      </CModalFooter>
    </CModal>
  )
}

export default EditCategoryModal
