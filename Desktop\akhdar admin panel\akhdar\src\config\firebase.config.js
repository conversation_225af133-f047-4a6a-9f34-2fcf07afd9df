// Import the functions you need from the SDKs you need
import { initializeApp } from 'firebase/app'
import { getAnalytics } from 'firebase/analytics'
import { getAuth } from 'firebase/auth'
import { getStorage, ref } from 'firebase/storage'
import { getFirestore } from 'firebase/firestore'
import { getFunctions } from 'firebase/functions'

const firebaseConfig = {
  apiKey: 'AIzaSyCJfY5cEIfc_dp87TDz26fq1T4R7TRMOSE',
  authDomain: 'akhdar-ddd67.firebaseapp.com',
  projectId: 'akhdar-ddd67',
  storageBucket: 'akhdar-ddd67.appspot.com',
  messagingSenderId: '875353902016',
  appId: '1:875353902016:web:2eaa8fba19cfb394145625',
  measurementId: 'G-V9W0HH2M3H',
}

// Initialize Firebase
export const app = initializeApp(firebaseConfig)
export const auth = getAuth(app)
export const storage = getStorage(app)
export const storageRef = ref(storage)
export const db = getFirestore(app)
export const functions = getFunctions(app)
