import React, { useEffect, useState } from 'react'
import {
  CCard,
  CCardBody,
  CCardHeader,
  CCol,
  CRow,
  CButton,
  CSpinner,
  CTable,
  CTableHead,
  CTableRow,
  CTableHeaderCell,
  CTableBody,
  CTableDataCell,
  CImage,
  CBadge,
  CModal,
  CModalHeader,
  CModalTitle,
  CModalBody,
  CModalFooter,
} from '@coreui/react'
import { useDispatch, useSelector } from 'react-redux'
import { fetchCategoriesThunk, deleteCategoryThunk } from '../../store/categories.slice'
import CIcon from '@coreui/icons-react'
import { cilPencil, cilTrash, cilPlus, cilList, cilInfo } from '@coreui/icons'
import AddCategoryModal from './AddCategoryModal'
import EditCategoryModal from './EditCategoryModal'
import ConfirmationModal from '../../components/ConfirmationModal'

const Categories = () => {
  const dispatch = useDispatch()
  // Add fallback empty object to prevent destructuring errors
  const {
    categories = [],
    isLoading = false,
    isDeleting = false,
  } = useSelector((state) => state.categories || {})

  const [addModalVisible, setAddModalVisible] = useState(false)
  const [editModalVisible, setEditModalVisible] = useState(false)
  const [detailsModalVisible, setDetailsModalVisible] = useState(false)
  const [selectedCategory, setSelectedCategory] = useState(null)

  const [deleteModalVisible, setDeleteModalVisible] = useState(false)
  const [categoryToDelete, setCategoryToDelete] = useState(null)
  useEffect(() => {
    dispatch(fetchCategoriesThunk())
  }, [dispatch])

  // Add console log to debug the data
  useEffect(() => {
    if (categories.length > 0) {
      console.log('Categories loaded:', categories)
    }
  }, [categories])

  // Improved filtering for parent categories with explicit debugging
  const parentCategories = categories.filter((cat) => {
    // More explicit check for null values
    if (cat.parentCategory === null) {
      console.log(`Category "${cat.title}" has null parentCategory - treating as parent`)
      return true
    }

    // Categories with "parentCategory" string value should be shown as parents
    if (cat.parentCategory === 'parentCategory') {
      console.log(`Category "${cat.title}" has 'parentCategory' string - treating as parent`)
      return true
    }

    // If parentCategory is undefined or empty string
    if (!cat.parentCategory) {
      console.log(`Category "${cat.title}" has empty/undefined parentCategory - treating as parent`)
      return true
    }

    // Check if the referenced parent exists
    const parentExists = categories.some((parent) => parent.id === cat.parentCategory)
    if (!parentExists) {
      console.log(`Category "${cat.title}" references non-existent parent - treating as parent`)
      return true
    }

    console.log(`Category "${cat.title}" is a subcategory with valid parent`)
    return false // It's a subcategory with valid parent reference
  })

  // Debug what's being filtered
  useEffect(() => {
    if (parentCategories.length === 0) {
      console.log('No parent categories found. Debug information:')
      categories.forEach((cat) => {})
    }
  }, [parentCategories])

  // Get subcategories for a parent category
  const getSubcategories = (parentId) => {
    return categories.filter(
      (cat) => cat.parentCategory === parentId || cat.parentCategoryId === parentId,
    )
  }

  const handleAddCategory = () => {
    setAddModalVisible(true)
  }

  const handleEditCategory = (category) => {
    setSelectedCategory(category)
    setEditModalVisible(true)
  }

  const handleViewDetails = (category) => {
    setSelectedCategory(category)
    setDetailsModalVisible(true)
  }

  const handleDeleteCategory = (id) => {
    setCategoryToDelete(id)
    setDeleteModalVisible(true)
  }
  const confirmDelete = () => {
    dispatch(deleteCategoryThunk(categoryToDelete))
    setDeleteModalVisible(false)
  }

  // Find parent category name by ID
  const getParentCategoryName = (parentId) => {
    const parent = categories.find((cat) => cat.id === parentId)
    return parent ? parent.title : 'None'
  }

  if (isLoading) {
    return (
      <div className="d-flex justify-content-center">
        <CSpinner color="primary" />
      </div>
    )
  }

  return (
    <>
      <CTable className="mb-4">
        <CCardHeader className="d-flex justify-content-between align-items-center">
          <h3>Categories</h3>
          <CButton
            style={{ backgroundColor: '#BE935E', color: '#fff', border: 'none' }}
            onClick={handleAddCategory}
          >
            Add Category
          </CButton>
        </CCardHeader>
        <CCardBody>
          <CTable hover responsive className="mt-1 overflow-x-auto">
            <CTableHead>
              <CTableRow>
                <CTableHeaderCell scope="col">#</CTableHeaderCell>
                <CTableHeaderCell scope="col">Icon</CTableHeaderCell>
                <CTableHeaderCell scope="col">Title</CTableHeaderCell>
                <CTableHeaderCell scope="col">Subcategories</CTableHeaderCell>
                <CTableHeaderCell scope="col">Actions</CTableHeaderCell>
              </CTableRow>
            </CTableHead>
            <CTableBody>
              {parentCategories.length > 0 ? (
                parentCategories.map((category, index) => (
                  <CTableRow key={category.id}>
                    <CTableHeaderCell scope="row">{index + 1}</CTableHeaderCell>
                    <CTableDataCell>
                      {category.icon?.url ? (
                        <CImage src={category.icon.url} width={40} height={40} />
                      ) : (
                        <div
                          style={{
                            width: 40,
                            height: 40,
                            backgroundColor: '#f0f0f0',
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                          }}
                        >
                          No Icon
                        </div>
                      )}
                    </CTableDataCell>
                    <CTableDataCell>{category.title}</CTableDataCell>
                    <CTableDataCell>
                      <CBadge color="info" className="ms-2">
                        {getSubcategories(category.id).length}
                      </CBadge>
                    </CTableDataCell>
                    <CTableDataCell>
                      <CButton
                        // color="info"
                        size="sm"
                        className="me-2 "
                        onClick={() => handleViewDetails(category)}
                      >
                        <CIcon icon={cilInfo} style={{ color: 'blue' }} />
                      </CButton>
                      <CButton
                        // color="info"
                        size="sm"
                        className="me-2 "
                        onClick={() => handleEditCategory(category)}
                      >
                        <CIcon icon={cilPencil} style={{ color: '#BE935E' }} />
                      </CButton>
                      <CButton
                        // color="danger"
                        size="sm"
                        // className="text-white"
                        onClick={() => handleDeleteCategory(category.id)}
                        disabled={isDeleting}
                      >
                        <CIcon icon={cilTrash} style={{ color: 'red' }} />
                      </CButton>
                    </CTableDataCell>
                  </CTableRow>
                ))
              ) : (
                <CTableRow>
                  <CTableDataCell colSpan="5" className="text-center">
                    No parent categories found
                  </CTableDataCell>
                </CTableRow>
              )}
            </CTableBody>
          </CTable>
        </CCardBody>
      </CTable>

      {/* Category Details Modal */}
      <CModal visible={detailsModalVisible} onClose={() => setDetailsModalVisible(false)} size="lg">
        <CModalHeader>
          <CModalTitle>
            {selectedCategory && (
              <div className="d-flex align-items-center">
                {selectedCategory.icon?.url && (
                  <p>
                    <CImage
                      src={selectedCategory.icon.url}
                      width={30}
                      height={30}
                      className="me-2"
                    />
                  </p>
                )}
                <p>{selectedCategory.title}</p>
              </div>
            )}
          </CModalTitle>
        </CModalHeader>
        <CModalBody>
          {selectedCategory && (
            <>
              {/* {selectedCategory.title && (
                <p>
                  <strong>Title:</strong> {selectedCategory.title}
                </p>
              )} */}

              {/* {selectedCategory.icon?.url && (
                <p>
                  <strong>Icon :</strong>{' '}
                  <CImage src={selectedCategory.icon.url} width={30} height={30} className="me-2" />
                </p>
              )} */}

              <h5 className="mt-1">Subcategories</h5>
              {getSubcategories(selectedCategory.id).length > 0 ? (
                <CTable hover responsive>
                  <CTableHead>
                    <CTableRow>
                      <CTableHeaderCell>#</CTableHeaderCell>
                      <CTableHeaderCell>Title</CTableHeaderCell>
                    </CTableRow>
                  </CTableHead>
                  <CTableBody>
                    {getSubcategories(selectedCategory.id).map((subcategory, index) => (
                      <CTableRow key={subcategory.id}>
                        <CTableHeaderCell scope="row">{index + 1}</CTableHeaderCell>
                        <CTableDataCell>{subcategory.title}</CTableDataCell>
                      </CTableRow>
                    ))}
                  </CTableBody>
                </CTable>
              ) : (
                <p className="text-muted">No subcategories found for this category.</p>
              )}
            </>
          )}
        </CModalBody>
        <CModalFooter></CModalFooter>
      </CModal>

      <AddCategoryModal
        visible={addModalVisible}
        onClose={() => setAddModalVisible(false)}
        parentCategories={parentCategories}
      />

      {selectedCategory && (
        <EditCategoryModal
          visible={editModalVisible}
          onClose={() => setEditModalVisible(false)}
          category={selectedCategory}
          parentCategories={parentCategories.filter((cat) => cat.id !== selectedCategory.id)}
        />
      )}

      <ConfirmationModal
        title="Delete Category"
        subtitle="Are you sure you want to delete this category? This action cannot be undone."
        confirmLabel="Delete"
        confirmColor="danger"
        visible={deleteModalVisible}
        closeModal={() => setDeleteModalVisible(false)}
        isLoading={isDeleting}
        onConfirm={confirmDelete}
      />
    </>
  )
}

export default Categories
