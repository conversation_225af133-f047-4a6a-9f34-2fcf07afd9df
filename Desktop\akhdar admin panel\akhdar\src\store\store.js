import { configureStore } from '@reduxjs/toolkit'
import uiReducer from './ui.slice'
import usersSlice from './users.slice'
import categoriesSlice from './categories.slice'
// import blogVideosSlice from './blogVideos.slice'

const store = configureStore({
  reducer: {
    ui: uiReducer.reducer,
    users: usersSlice.reducer,
    categories: categoriesSlice.reducer,
    // blogVideos: blogVideosSlice.reducer,
  },
  // middleware: getDefaultMiddleware({
  //   serializableCheck: {
  //     ignoredActions: ['yourActionType'],
  //     ignoredPaths: ['nonSerializablePath'],
  //   },
  // }),
})

export default store
