import React, { useState } from 'react'
import {
  CButton,
  CModal,
  CModalHeader,
  CModalTitle,
  CModalBody,
  CModalFooter,
  CForm,
  CFormInput,
  CFormLabel,
  CFormSelect,
  CSpinner,
  CImage,
  CFormText,
  CInputGroup,
} from '@coreui/react'
import { useDispatch, useSelector } from 'react-redux'
import { addCategoryThunk, addNewCategoryAction } from '../../store/categories.slice'
import { getStorage, ref, uploadBytes, getDownloadURL } from 'firebase/storage'
import { collection, getDocs, addDoc, updateDoc, deleteDoc, doc } from 'firebase/firestore'
import { db, auth } from '../../config/firebase.config'
import { COLLECTIONS } from '../../constants/index'

const AddCategoryModal = ({ visible, onClose, parentCategories }) => {
  const [formData, setFormData] = useState({
    title: '',
    icon: { url: '' },
    // parentCategory: null,
    subcategories: [],
  })
  const [uploadedImage, setUploadedImage] = useState(null)
  const [previewUrl, setPreviewUrl] = useState('')
  const [isUploading, setIsUploading] = useState(false)
  const [errors, setErrors] = useState({})
  const [subcategoryInput, setSubcategoryInput] = useState('')
  const dispatch = useDispatch()
  const { isAdding } = useSelector((state) => state.categories || { isAdding: false })

  const handleChange = (e) => {
    const { name, value } = e.target

    if (name === 'iconUrl') {
      setFormData({
        ...formData,
        icon: { url: value },
      })
    } else if (name === 'parentCategory') {
      setFormData({
        ...formData,
        parentCategory: value === '' ? null : value,
      })
    } else {
      setFormData({
        ...formData,
        [name]: value,
      })
    }

    // Clear error when user types
    if (errors[name]) {
      setErrors({
        ...errors,
        [name]: null,
      })
    }
  }

  const handleSubcategoryInputChange = (e) => {
    setSubcategoryInput(e.target.value)
  }

  const addSubcategory = () => {
    if (subcategoryInput.trim()) {
      setFormData({
        ...formData,
        subcategories: [...formData.subcategories, subcategoryInput.trim()],
      })
      setSubcategoryInput('') // Clear the input after adding
    }
  }

  const removeSubcategory = (index) => {
    const updatedSubcategories = [...formData.subcategories]
    updatedSubcategories.splice(index, 1)
    setFormData({
      ...formData,
      subcategories: updatedSubcategories,
    })
  }

  const handleFileChange = (e) => {
    const file = e.target.files[0]
    if (!file) return

    // Clear previous errors
    if (errors.icon) {
      setErrors({
        ...errors,
        icon: null,
      })
    }

    // Validate file type
    const validTypes = ['image/jpeg', 'image/png', 'image/webp']
    if (!validTypes.includes(file.type)) {
      setErrors({
        ...errors,
        icon: 'Invalid file type. Please upload a JPEG, PNG, GIF, or WEBP image.',
      })
      return
    }

    // Validate file size (max 2MB)
    if (file.size > 2 * 1024 * 1024) {
      setErrors({
        ...errors,
        icon: 'File is too large. Maximum size is 2MB.',
      })
      return
    }

    setUploadedImage(file)

    // Create preview
    const reader = new FileReader()
    reader.onloadend = () => {
      setPreviewUrl(reader.result)
    }
    reader.readAsDataURL(file)
  }

  const uploadIcon = async (file) => {
    if (!file) return null

    try {
      setIsUploading(true)
      const storage = getStorage()
      const timestamp = new Date().getTime()
      const fileName = `category-icons/${timestamp}_${file.name.replace(/\s+/g, '_')}`
      const storageRef = ref(storage, fileName)

      await uploadBytes(storageRef, file)
      const downloadUrl = await getDownloadURL(storageRef)

      return downloadUrl
    } catch (error) {
      console.error('Error uploading file:', error)
      setErrors({
        ...errors,
        icon: 'Failed to upload image. Please try again.',
      })
      return null
    } finally {
      setIsUploading(false)
    }
  }

  const validateForm = () => {
    const newErrors = {}

    if (!formData.title.trim()) {
      newErrors.title = 'Title is required'
    }

    if (!uploadedImage && !formData.icon.url.trim()) {
      newErrors.icon = 'Please upload an icon or provide an icon URL'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = async (e) => {
    e.preventDefault()

    if (validateForm()) {
      try {
        // First create the main category
        const categoryData = {
          title: formData.title,
          icon: formData.icon,
          createdAt: new Date(),
          // No subcategories field in main document
        }

        // If there's an uploaded image, upload it to Firebase Storage
        if (uploadedImage) {
          const iconUrl = await uploadIcon(uploadedImage)
          if (iconUrl) {
            categoryData.icon = { url: iconUrl }
          } else {
            return // Stop if upload failed
          }
        }

        // Create the parent category first
        const docRef = await addDoc(collection(db, COLLECTIONS.CATEGORIES), categoryData)
        const parentCategoryId = docRef.id

        // Now create subcategory documents if any exist
        const subcategoryPromises = formData.subcategories.map(async (subcategoryTitle) => {
          const subcategoryData = {
            title: subcategoryTitle,
            parentCategory: parentCategoryId,
            createdAt: new Date(),
          }
          return addDoc(collection(db, COLLECTIONS.CATEGORIES), subcategoryData)
        })

        // Wait for all subcategory documents to be created
        await Promise.all(subcategoryPromises)

        // Add to Redux state with subcategories as a temporary field for display
        dispatch(
          addNewCategoryAction({
            id: parentCategoryId,
            ...categoryData,
            subcategories: formData.subcategories, // Include for UI display
          }),
        )

        onClose()

        // Reset form
        setFormData({
          title: '',
          icon: { url: '' },
          subcategories: [],
        })
        setUploadedImage(null)
        setPreviewUrl('')
        setSubcategoryInput('')
      } catch (error) {
        console.error('Error creating category:', error)
        setErrors({
          ...errors,
          form: 'Failed to create category. Please try again.',
        })
      }
    }
  }

  return (
    <CModal visible={visible} onClose={onClose} backdrop="static">
      <CModalHeader>
        <CModalTitle>Add Category</CModalTitle>
      </CModalHeader>
      <CModalBody>
        <CForm onSubmit={handleSubmit}>
          <div className="mb-3">
            <CFormLabel htmlFor="title">Title</CFormLabel>
            <CFormInput
              type="text"
              id="title"
              name="title"
              value={formData.title}
              onChange={handleChange}
              invalid={!!errors.title}
            />
            {errors.title && <div className="text-danger">{errors.title}</div>}
          </div>
          <div className="mb-3">
            <CFormLabel>Icon</CFormLabel>
            <div className="mb-2">
              <input
                type="file"
                className="form-control"
                id="iconFile"
                accept=".png,.jpg,.jpeg,.webp"
                onChange={handleFileChange}
              />
              <CFormText>Upload an image (JPEG, PNG, GIF, WEBP, max 2MB)</CFormText>
            </div>

            {previewUrl && (
              <div className="mb-3 text-center">
                <p>Preview:</p>
                <CImage src={previewUrl} width={80} height={80} className="border rounded p-1" />
              </div>
            )}

            {/* <div className="mt-2">
              <CFormLabel htmlFor="iconUrl">Or enter icon URL</CFormLabel>
              <CFormInput
                type="text"
                id="iconUrl"
                name="iconUrl"
                value={formData.icon.url}
                onChange={handleChange}
                placeholder="https://example.com/icon.png"
                disabled={!!uploadedImage}
              />
              <CFormText>Either upload an image or provide a URL, not both</CFormText>
            </div> */}

            {errors.icon && <div className="text-danger mt-2">{errors.icon}</div>}
          </div>
          <div className="mb-3">
            <CFormLabel>Subcategories</CFormLabel>
            <CInputGroup className="mb-2">
              <CFormInput
                placeholder="Enter subcategory"
                value={subcategoryInput}
                onChange={handleSubcategoryInputChange}
                onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addSubcategory())}
                aria-describedby="button-add-subcategory"
              />
              <CButton
                type="button"
                style={{ backgroundColor: '#BE935E', color: '#fff' }}
                id="button-add-subcategory"
                onClick={addSubcategory}
              >
                Add
              </CButton>
            </CInputGroup>

            {formData.subcategories.length > 0 && (
              <div className="mt-2">
                <p className="mb-2 font-medium">Added subcategories:</p>
                <div className="d-flex flex-wrap gap-2  ">
                  {/* Display added subcategories */}
                  {formData.subcategories.map((subcategory, index) => (
                    <div
                      key={index}
                      className="d-flex justify-content-center align-items-center  bg-secondary bg-opacity-10 text-dark px-2   rounded "
                      style={{ fontWeight: '600', fontSize: '14px' }}
                    >
                      <span>{subcategory}</span>
                      <CButton
                        color="light"
                        size="sm"
                        className="ms-2 p-0 border-0"
                        style={{
                          fontSize: '16px',
                          lineHeight: '1',
                          color: 'gray',
                        }}
                        onClick={() => removeSubcategory(index)}
                      >
                        ×
                      </CButton>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        </CForm>
      </CModalBody>
      <CModalFooter>
        <CButton
          style={{ backgroundColor: '#BE935E', color: '#fff' }}
          onClick={handleSubmit}
          disabled={isAdding || isUploading}
        >
          {isAdding || isUploading ? (
            <>
              <CSpinner size="sm" className="me-2" />
              {isUploading ? 'Uploading...' : 'Adding...'}
            </>
          ) : (
            'Add Category'
          )}
        </CButton>
      </CModalFooter>
    </CModal>
  )
}

export default AddCategoryModal
