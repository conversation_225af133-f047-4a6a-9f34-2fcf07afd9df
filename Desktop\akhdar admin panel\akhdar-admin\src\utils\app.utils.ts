import { doc, getDoc } from 'firebase/firestore'
import { db } from '../config/firebase.config'

export async function isLoggedIn(): Promise<boolean> {
  const userId = localStorage.getItem('userId')
  if (!userId) {
    return false
  }
  // CHECK USER IS ADMIN
  const userData = await fetchUser(userId)
  if (userData && userData?.userType === 'admin') {
    return true
  }
  return false
}

// GET USER THROUGH UUID
export async function fetchUser(id: string): Promise<any> {
  const userRef = doc(db, 'Users', id)
  const userSnap = await getDoc(userRef)
  if (userSnap.exists()) {
    return userSnap.data()
  } else return null
}

// GET COUNTS FOR DASHBOARD
export async function getDashboardCounts(): Promise<{
  managers: number
  staff: number
  clients: number
}> {
  // This would typically fetch from Firestore collections
  // For now, returning mock data - replace with actual Firestore queries
  return {
    managers: 12,
    staff: 45,
    clients: 234
  }
}
