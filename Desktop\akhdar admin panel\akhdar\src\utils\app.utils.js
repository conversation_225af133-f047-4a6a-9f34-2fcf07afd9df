import { doc, getDoc } from 'firebase/firestore'
import { db } from '../config/firebase.config'

async function isLoggedIn() {
  const userId = localStorage.getItem('userId')
  if (!userId) {
    return false
  }
  // CHECK USER IS ADMIN
  const userData = await fetchUser(userId)
  if (userData && userData?.userType === 'admin') {
    return true
  }
  return false
}

// GET USER THROUGHT UUID
async function fetchUser(id) {
  const userRef = doc(db, 'Users', id)
  const userSnap = await getDoc(userRef)
  if (userSnap.exists()) {
    return userSnap.data()
  } else return null
}

export { isLoggedIn, fetchUser }
