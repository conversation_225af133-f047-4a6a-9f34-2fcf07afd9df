import { createAsyncThunk, createSlice } from '@reduxjs/toolkit'
import {
  collection,
  getDocs,
  query,
  deleteDoc,
  updateDoc,
  where,
  doc,
  setDoc,
} from 'firebase/firestore'
import { db, auth } from '../config/firebase.config'
import { COLLECTIONS } from '../constants'
import { createUserWithEmailAndPassword } from 'firebase/auth'

// import { AGE_GROUPS } from '../constants'

const initialState = {
  isLoading: false,
  isCreating: false,
  isUpdating: false,
  results: [],
  filteredResults: [],
  filters: {
    ageGroup: null,
    currentRole: 'Mentor',
  },
  userDetails: null,
  error: null,
}

export const fetchUsersThunk = createAsyncThunk(
  'users/fetchUsersThunk',
  async (userType = null, { rejectWithValue, fulfillWithValue }) => {
    try {
      let q
      if (userType) {
        // Filter by userType if provided
        q = query(collection(db, COLLECTIONS.USERS), where('userType', '==', userType))
      } else {
        // Get all users if no userType specified
        q = query(collection(db, COLLECTIONS.USERS))
      }

      const querySnapshot = await getDocs(q)
      const users_arr = querySnapshot.docs.map((doc) => ({ id: doc.id, ...doc.data() }))
      return fulfillWithValue(users_arr)
    } catch (error) {
      console.log('fetchUsersThunk >>', error)
      return rejectWithValue('Error while fetching users from Firebase')
    }
  },
)

const usersSlice = createSlice({
  name: 'users',
  initialState,
  reducers: {
    setUserDetailsAction: (state, action) => {
      state.userDetails = action.payload
      return state
    },
    updateUserAction: (state, action) => {
      const index = state.results.findIndex((item) => item?.id === action.payload?.id)
      if (index !== -1) {
        state.results[index] = {
          ...state.results[index],
          ...action.payload,
        }
      }
      return state
    },
    deleteUserAction: (state, action) => {
      state.results = state.results.filter((item) => item?.id !== action.payload?.id)
      return state
    },
    // filterUsersAction: (state, action) => {
    //   state.filters.ageGroup = action.payload.ageGroup
    //   state.filters.currentRole = action.payload.currentRole || 'Mentor'
    //   let filtered_users = state.results.filter(
    //     (item) => item?.userType === state.filters.currentRole,
    //   )
    //   const ageGroup = AGE_GROUPS.find((item) => item.label === action.payload.ageGroup)
    //   if (ageGroup) {
    //     filtered_users = filtered_users.filter(
    //       (item) => item?.age >= ageGroup?.minmax[0] && item?.age <= ageGroup?.minmax[1],
    //     )
    //   }
    //   state.filteredResults = filtered_users
    //   return state
    // },
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchUsersThunk.pending, (state) => {
        state.error = null
        state.isLoading = true
      })

      .addCase(fetchUsersThunk.fulfilled, (state, action) => {
        state.isLoading = false
        state.results = action.payload
        state.filteredResults = action.payload.filter(
          (item) => item?.userType === state.filters.currentRole,
        )
      })
      .addCase(fetchUsersThunk.rejected, (state, { payload }) => {
        state.isLoading = false
        state.error = payload
      })

      .addCase(updateUserThunk.pending, (state) => {
        state.error = null
        state.isUpdating = true
      })
      .addCase(updateUserThunk.fulfilled, (state) => {
        state.isUpdating = false
      })
      .addCase(updateUserThunk.rejected, (state, { payload }) => {
        state.isUpdating = false
        state.error = payload
      })
      .addCase(deleteUserThunk.rejected, (state, { payload }) => {
        state.error = payload
      })
  },
})

export const deleteUserThunk = createAsyncThunk(
  'users/deleteUserThunk',
  async (userId, { rejectWithValue, fulfillWithValue, dispatch }) => {
    try {
      await deleteDoc(doc(db, COLLECTIONS.USERS, userId))

      // Dispatch the action to update the Redux state
      dispatch(deleteUserAction({ id: userId }))

      return fulfillWithValue(userId)
    } catch (error) {
      console.error('deleteUserThunk error:', error)
      return rejectWithValue('Error deleting user: ' + error.message)
    }
  },
)

export const updateUserThunk = createAsyncThunk(
  'users/updateUserThunk',
  async (userData, { rejectWithValue, fulfillWithValue, dispatch }) => {
    try {
      const userRef = doc(db, COLLECTIONS.USERS, userData.id)

      const { id, ...dataToUpdate } = userData

      await updateDoc(userRef, dataToUpdate)

      dispatch(updateUserAction(userData))

      return fulfillWithValue(userData)
    } catch (error) {
      console.error('updateUserThunk error:', error)
      return rejectWithValue('Error updating user: ' + error.message)
    }
  },
)

export const { setUserDetailsAction, deleteUserAction, updateUserAction, filterUsersAction } =
  usersSlice.actions

export default usersSlice
