import React, { useState } from 'react'
import { Link, useNavigate } from 'react-router-dom'
import { toast } from 'react-toastify'

import {
  CButton,
  CCard,
  CCardBody,
  CCardGroup,
  CCol,
  CContainer,
  CForm,
  CFormInput,
  CInputGroup,
  CInputGroupText,
  CRow,
  CSpinner,
} from '@coreui/react'
import CIcon from '@coreui/icons-react'
import { cilLockLocked, cilUser } from '@coreui/icons'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { signInWithEmailAndPassword } from 'firebase/auth'
import { auth } from '../../../config/firebase.config'
import { fetchUser } from '../../../utils/app.utils'

const signInValidator = z.object({
  email: z
    .string()
    .min(1, { message: 'Email is required' })
    .email({ message: 'Please enter a valid email address' }),
  password: z
    .string()
    .min(8, { message: 'Password must be at least 8 characters' })
    .max(100, { message: 'Password is too long' }),
})

const Login = () => {
  const [isLoading, setLoading] = useState(false)
  const navigate = useNavigate()

  const {
    handleSubmit,
    formState: { errors },
    register,
  } = useForm({
    defaultValues: {
      email: '',
      password: '',
    },
    resolver: zodResolver(signInValidator),
    mode: 'onBlur',
  })

  async function signInWithCredentials({ email, password }) {
    setLoading(true)
    signInWithEmailAndPassword(auth, email, password)
      .then(async (response) => {
        const userData = await fetchUser(response?.user?.uid)

        console.log('userData', userData)
        // USER IS ADMIN
        if (userData && userData?.userType === 'admin') {
          localStorage.setItem('userId', response?.user?.uid)
          localStorage.setItem('userData', JSON.stringify(userData))
          toast.success('Signed in successfully.')
          navigate('/users')
        } else {
          toast.error("This user doesn't exisit.")
        }
      })
      .catch((error) => {
        console.log(error)
        setLoading(false)
        // USER NOT EXISTS
        if (error.code === 'auth/user-not-found') {
          toast.error('This email does not exist.')
        }
        // INVAVLID CREDENTIALS
        else if (error.code === 'auth/wrong-password' || 'auth/invalid-login-credentials') {
          toast.error('Invalid email or password')
        } else {
          toast.error('Something went wrong.')
        }
      })
  }

  return (
    <div className="bg-body-tertiary min-vh-100 d-flex flex-row align-items-center">
      <CContainer>
        <CRow className="justify-content-center">
          <CCol md={8} lg={6} sm={10}>
            <CCardGroup>
              <CCard className="p-4">
                <CCardBody>
                  <form onSubmit={handleSubmit(signInWithCredentials)}>
                    <h1>Sign In</h1>
                    <p className="text-body-secondary">Sign In to your account</p>
                    <CInputGroup className="mb-3">
                      <CInputGroupText>
                        <CIcon icon={cilUser} />
                      </CInputGroupText>
                      <CFormInput placeholder="Email" type="email" {...register('email')} />
                    </CInputGroup>
                    {errors?.email ? <p className="text-danger">{errors?.email.message}</p> : null}
                    <CInputGroup className="mb-4">
                      <CInputGroupText>
                        <CIcon icon={cilLockLocked} />
                      </CInputGroupText>
                      <CFormInput
                        type="password"
                        placeholder="Password"
                        autoComplete="current-password"
                        {...register('password')}
                      />
                    </CInputGroup>
                    {errors?.password ? (
                      <p className="text-danger">{errors?.password?.message}</p>
                    ) : null}
                    <div className="d-flex justify-content-end">
                      <CButton
                        color="primary"
                        className="px-4 d-flex justify-content-center align-items-center"
                        type="submit"
                        disabled={isLoading}
                      >
                        Sign In
                        {isLoading ? <CSpinner className="ms-2" size="sm" /> : null}
                      </CButton>
                    </div>
                  </form>
                </CCardBody>
              </CCard>
            </CCardGroup>
          </CCol>
        </CRow>
      </CContainer>
    </div>
  )
}

export default Login
