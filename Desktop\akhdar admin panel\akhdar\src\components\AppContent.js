import React, { Suspense } from 'react'
import { Navigate, Route, Routes } from 'react-router-dom'
import { <PERSON><PERSON>r, CSpinner } from '@coreui/react'

// routes config
import routes from '../routes'

const AppContent = () => {
  const userId = localStorage.getItem('userId')
  const userData = JSON.parse(localStorage.getItem('userData'))

  return (
    <CContainer className="px-3" lg>
      <Suspense>
        <Routes>
          {routes.map((route, idx) => {
            return (
              route.element && (
                <Route
                  key={idx}
                  path={route.path}
                  exact={route.exact}
                  name={route.name}
                  element={
                    userId && userData?.userType === 'admin' ? (
                      <route.element />
                    ) : (
                      <Navigate to="/" />
                    )
                  }
                />
              )
            )
          })}
        </Routes>
      </Suspense>
    </CContainer>
  )
}

export default React.memo(AppContent)
