import { useEffect, useState } from 'react'
import UserTable from '../components/UserTable'
import { Plus } from 'lucide-react'
import { toast } from 'sonner'
import type { User } from '../types/user'

const Users = () => {
  const [activeTab, setActiveTab] = useState<'manager' | 'staff' | 'client'>('manager')
  const [isLoading, setIsLoading] = useState(true)
  const [users, setUsers] = useState<User[]>([])

  useEffect(() => {
    // Simulate loading users data
    const loadUsers = async () => {
      setIsLoading(true)
      try {
        // Simulate API call with mock data
        await new Promise(resolve => setTimeout(resolve, 1000))
        const mockUsers: User[] = [
          {
            id: '1',
            name: '<PERSON>',
            email: '<EMAIL>',
            userType: 'manager',
            createdAt: new Date().toISOString()
          },
          {
            id: '2',
            name: '<PERSON>',
            email: '<EMAIL>',
            userType: 'staff',
            createdAt: new Date().toISOString()
          },
          {
            id: '3',
            name: '<PERSON>',
            email: '<EMAIL>',
            userType: 'client',
            createdAt: new Date().toISOString()
          },
          {
            id: '4',
            name: 'Alice <PERSON>',
            email: '<EMAIL>',
            userType: 'manager',
            createdAt: new Date().toISOString()
          },
          {
            id: '5',
            name: '<PERSON>',
            email: '<EMAIL>',
            userType: 'staff',
            createdAt: new Date().toISOString()
          }
        ]
        setUsers(mockUsers)
      } catch (error) {
        toast.error('Failed to load users')
      } finally {
        setIsLoading(false)
      }
    }

    loadUsers()
  }, [])

  const tabs = [
    { id: 'manager', label: 'Managers', color: 'blue' },
    { id: 'staff', label: 'Staff', color: 'green' },
    { id: 'client', label: 'Clients', color: 'purple' },
  ] as const

  const filteredUsers = users.filter((user: User) => user.userType === activeTab)

  return (
    <div className="p-6">
      {/* Header */}
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Users Management</h1>
          <p className="text-gray-600 mt-1">Manage managers, staff, and clients</p>
        </div>
        <button className="flex items-center space-x-2 bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors">
          <Plus className="w-4 h-4" />
          <span>Add User</span>
        </button>
      </div>

      {/* Loading state */}
      {isLoading && (
        <div className="flex justify-center items-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>
      )}

      {/* Tabs */}
      {!isLoading && users.length > 0 && (
        <>
          <div className="border-b border-gray-200 mb-6">
            <nav className="-mb-px flex space-x-8">
              {tabs.map((tab) => {
                const isActive = activeTab === tab.id
                const userCount = users.filter((user: User) => user.userType === tab.id).length

                return (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    className={`py-2 px-1 border-b-2 font-medium text-sm transition-colors ${
                      isActive
                        ? `border-${tab.color}-500 text-${tab.color}-600`
                        : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                    }`}
                  >
                    {tab.label}
                    <span className={`ml-2 py-0.5 px-2 rounded-full text-xs ${
                      isActive
                        ? `bg-${tab.color}-100 text-${tab.color}-600`
                        : 'bg-gray-100 text-gray-600'
                    }`}>
                      {userCount}
                    </span>
                  </button>
                )
              })}
            </nav>
          </div>

          {/* User Table */}
          <UserTable users={filteredUsers} userType={activeTab} />
        </>
      )}

      {/* Empty state */}
      {!isLoading && users.length === 0 && (
        <div className="text-center py-12">
          <div className="w-24 h-24 mx-auto mb-4 bg-gray-100 rounded-full flex items-center justify-center">
            <Plus className="w-8 h-8 text-gray-400" />
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">No users found</h3>
          <p className="text-gray-500 mb-4">Get started by adding your first user.</p>
          <button className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors">
            Add User
          </button>
        </div>
      )}
    </div>
  )
}

export default Users
