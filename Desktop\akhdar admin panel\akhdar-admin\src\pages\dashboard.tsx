import { useEffect, useState } from 'react'
import { useNavigate } from 'react-router-dom'
import { getDashboardCounts } from '../utils/app.utils'
import { toast } from 'sonner'
import { Users, UserCheck, Building2, TrendingUp, Activity, Clock } from 'lucide-react'

interface DashboardCounts {
  managers: number
  staff: number
  clients: number
}

export default function DashboardPage() {
  const [counts, setCounts] = useState<DashboardCounts>({
    managers: 0,
    staff: 0,
    clients: 0
  })
  const [isLoading, setIsLoading] = useState(true)
  const navigate = useNavigate()

  useEffect(() => {
    loadDashboardData()
  }, [])

  async function loadDashboardData() {
    try {
      const data = await getDashboardCounts()
      setCounts(data)
    } catch (error) {
      console.error('Error loading dashboard data:', error)
      toast.error('Failed to load dashboard data')
    } finally {
      setIsLoading(false)
    }
  }

  const statsCards = [
    {
      title: 'Total Managers',
      value: counts.managers,
      icon: Building2,
      color: 'blue',
      change: '+2.5%',
      changeText: 'from last month'
    },
    {
      title: 'Total Staff',
      value: counts.staff,
      icon: UserCheck,
      color: 'green',
      change: '+5.2%',
      changeText: 'from last month'
    },
    {
      title: 'Total Clients',
      value: counts.clients,
      icon: Users,
      color: 'purple',
      change: '+12.3%',
      changeText: 'from last month'
    }
  ]

  const recentActivities = [
    {
      title: 'New manager added',
      time: '2 hours ago',
      color: 'blue'
    },
    {
      title: '5 new staff members joined',
      time: '1 day ago',
      color: 'green'
    },
    {
      title: '15 new clients registered',
      time: '2 days ago',
      color: 'purple'
    },
    {
      title: 'System backup completed',
      time: '3 days ago',
      color: 'gray'
    }
  ]

  const quickActions = [
    {
      title: 'Manage Managers',
      icon: Building2,
      path: '/users',
      description: 'Add, edit, or remove managers'
    },
    {
      title: 'Manage Staff',
      icon: UserCheck,
      path: '/users',
      description: 'Oversee staff members'
    },
    {
      title: 'Manage Clients',
      icon: Users,
      path: '/users',
      description: 'Handle client accounts'
    }
  ]

  return (
    <div className="p-6">
      {/* Header */}
      <div className="mb-8">
        <h1 className="text-2xl font-bold text-gray-900 mb-2">Dashboard Overview</h1>
        <p className="text-gray-600">
          Monitor your organization's key metrics and performance indicators.
        </p>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        {statsCards.map((stat, index) => {
          const Icon = stat.icon
          return (
            <div key={index} className="bg-white rounded-lg border border-gray-200 p-6 hover:shadow-lg transition-shadow duration-200">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-sm font-medium text-gray-600">{stat.title}</h3>
                <div className={`p-2 rounded-lg bg-${stat.color}-100`}>
                  <Icon className={`w-5 h-5 text-${stat.color}-600`} />
                </div>
              </div>
              <div className="text-3xl font-bold text-gray-900 mb-2">
                {isLoading ? '...' : stat.value.toLocaleString()}
              </div>
              <div className="flex items-center text-sm">
                <TrendingUp className="w-4 h-4 text-green-500 mr-1" />
                <span className="text-green-600 font-medium">{stat.change}</span>
                <span className="text-gray-500 ml-1">{stat.changeText}</span>
              </div>
            </div>
          )
        })}
      </div>

      {/* Content Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Recent Activity */}
        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <div className="flex items-center mb-6">
            <Activity className="w-5 h-5 text-gray-600 mr-2" />
            <h2 className="text-lg font-semibold text-gray-900">Recent Activity</h2>
          </div>
          <div className="space-y-4">
            {recentActivities.map((activity, index) => (
              <div key={index} className="flex items-center space-x-3">
                <div className={`w-2 h-2 bg-${activity.color}-500 rounded-full`}></div>
                <div className="flex-1">
                  <p className="text-sm font-medium text-gray-900">{activity.title}</p>
                  <div className="flex items-center text-xs text-gray-500">
                    <Clock className="w-3 h-3 mr-1" />
                    {activity.time}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Quick Actions */}
        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-6">Quick Actions</h2>
          <div className="space-y-3">
            {quickActions.map((action, index) => {
              const Icon = action.icon
              return (
                <button
                  key={index}
                  onClick={() => navigate(action.path)}
                  className="w-full flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 hover:border-gray-300 transition-colors text-left"
                >
                  <div className="p-2 bg-gray-100 rounded-lg mr-4">
                    <Icon className="w-5 h-5 text-gray-600" />
                  </div>
                  <div>
                    <h3 className="font-medium text-gray-900">{action.title}</h3>
                    <p className="text-sm text-gray-500">{action.description}</p>
                  </div>
                </button>
              )
            })}
          </div>
        </div>
      </div>
    </div>
  )
}
