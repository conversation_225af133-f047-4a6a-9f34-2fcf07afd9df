import React, { useEffect, useState } from 'react'
import { CButton, CNav, CNavItem, CNavLink, CSpinner, CTabContent, CTabPane } from '@coreui/react'
import Usertable from './UserTable'
import AddUserModal from './AddUserModal'

import { useDispatch, useSelector } from 'react-redux'
import { fetchUsersThunk } from '../../store/users.slice'
const Users = () => {
  const [activeKey, setActiveKey] = useState('manager')
  const [modalVisible, setModalVisible] = useState(false)

  const dispatch = useDispatch()
  const { isLoading, results } = useSelector((state) => state.users)

  useEffect(() => {
    if (!results?.length) {
      dispatch(fetchUsersThunk())
    }
  }, [results])

  return (
    <>
      <div className="d-flex justify-content-between align-items-center mb-3">
        <h3>Users</h3>
        <CButton
          style={{ backgroundColor: '#BE935E', color: 'white' }}
          onClick={() => setModalVisible(true)}
        >
          Add User
        </CButton>
      </div>

      {isLoading ? (
        <div className="d-flex justify-content-center">
          <CSpinner />
        </div>
      ) : null}

      {results.length ? (
        <>
          <CNav variant="tabs" role="tablist" layout="fill">
            <CNavItem>
              <CNavLink
                active={activeKey === 'manager'}
                onClick={() => setActiveKey('manager')}
                style={
                  activeKey === 'manager'
                    ? { backgroundColor: '#BE935E', color: 'white' }
                    : { color: 'black' }
                }
              >
                Manager
              </CNavLink>
            </CNavItem>
            <CNavItem>
              <CNavLink
                active={activeKey === 'staff'}
                onClick={() => setActiveKey('staff')}
                style={
                  activeKey === 'staff'
                    ? { backgroundColor: '#BE935E', color: 'white' }
                    : { color: 'black' }
                }
              >
                Staff
              </CNavLink>
            </CNavItem>
            <CNavItem>
              <CNavLink
                active={activeKey === 'client'}
                onClick={() => setActiveKey('client')}
                style={
                  activeKey === 'client'
                    ? { backgroundColor: '#BE935E', color: 'white' }
                    : { color: 'black' }
                }
              >
                Clients
              </CNavLink>
            </CNavItem>
          </CNav>
          <CTabContent>
            <CTabPane role="tabpanel" aria-labelledby="home-tab" visible>
              <Usertable activeType={activeKey} />
            </CTabPane>
          </CTabContent>
        </>
      ) : null}
      <AddUserModal visible={modalVisible} onClose={() => setModalVisible(false)} />
    </>
  )
}

export default Users
