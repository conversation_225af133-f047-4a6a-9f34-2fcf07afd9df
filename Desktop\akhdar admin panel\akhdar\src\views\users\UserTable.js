import {
  <PERSON>pinner,
  CTable,
  CTableBody,
  CTableDataCell,
  CTableHead,
  CTableHeaderCell,
  CTableRow,
  CButton,
} from '@coreui/react'
import { useSelector, useDispatch } from 'react-redux'
import { deleteUserThunk } from '../../store/users.slice'
import EditUserModal from './EditUserModal'
import { useState } from 'react'
import ConfirmationModal from '../../components/ConfirmationModal'
import EmptyBox from '../../components/EmptyBox'

const Usertable = ({ activeType }) => {
  const dispatch = useDispatch()

  const { results, isDeleting } = useSelector((state) => state.users)
  const filtered_users = results.filter((user) => user.userType === activeType)

  const [editModalVisible, setEditModalVisible] = useState(false)
  const [selectedUser, setSelectedUser] = useState(null)

  // Add state for delete confirmation modal
  const [deleteModalVisible, setDeleteModalVisible] = useState(false)
  const [userToDelete, setUserToDelete] = useState(null)

  const handleEdit = (user) => {
    setSelectedUser(user)
    setEditModalVisible(true)
  }

  // Update handleDelete to open the confirmation modal instead
  const handleDelete = (userId) => {
    setUserToDelete(userId)
    setDeleteModalVisible(true)
  }

  // Actual delete function when confirmed
  const confirmDelete = () => {
    dispatch(deleteUserThunk(userToDelete))
    setDeleteModalVisible(false)
  }

  return (
    <>
      {filtered_users.length === 0 ? (
        <EmptyBox label={`No ${activeType}  found`} />
      ) : (
        <CTable className="mt-1 overflow-x-auto">
          <CTableHead>
            <CTableRow>
              <CTableHeaderCell scope="col">#</CTableHeaderCell>
              <CTableHeaderCell scope="col">Name</CTableHeaderCell>
              <CTableHeaderCell scope="col">Email</CTableHeaderCell>
              <CTableHeaderCell scope="col">Actions</CTableHeaderCell>
            </CTableRow>
          </CTableHead>
          <CTableBody>
            {filtered_users.map((user, index) => (
              <CTableRow key={user.id}>
                <CTableHeaderCell scope="row">{index + 1}</CTableHeaderCell>
                <CTableDataCell>{user.name}</CTableDataCell>
                <CTableDataCell>{user.email}</CTableDataCell>
                <CTableDataCell>
                  <CButton
                    color="info"
                    size="md"
                    className="me-2 text-white"
                    onClick={() => handleEdit(user)}
                  >
                    Edit
                  </CButton>
                  <CButton
                    color="danger"
                    size="md"
                    className="text-white"
                    onClick={() => handleDelete(user.id)}
                  >
                    Delete
                  </CButton>
                </CTableDataCell>
              </CTableRow>
            ))}
          </CTableBody>
        </CTable>
      )}

      <EditUserModal
        visible={editModalVisible}
        onClose={() => setEditModalVisible(false)}
        user={selectedUser}
      />

      {/* Add the confirmation modal */}
      <ConfirmationModal
        title="Delete User"
        subtitle="Are you sure you want to delete this user? This action cannot be undone."
        confirmLabel="Delete"
        confirmColor="danger"
        visible={deleteModalVisible}
        closeModal={() => setDeleteModalVisible(false)}
        isLoading={isDeleting}
        onConfirm={confirmDelete}
      />
    </>
  )
}
export default Usertable
